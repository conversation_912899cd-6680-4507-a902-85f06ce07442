---
import PublicationsFilter from "./PublicationsFilter.vue";
import PublicationType from "./PublicationType.astro";
import PublicationCard from "./PublicationCard.astro";
import { typeStyles, type PublicationSection } from ".";

interface Props {
    data: PublicationSection;
}

const {
    title,
    publications,
    publishedTitle,
    publishedIcon: PublishedIcon,
    unpublishedTitle,
    unpublishedIcon: UnpublishedIcon,
    afterText,
} = Astro.props.data;

const publishedPublications = publications.filter((pub) => pub.type === "published");
const unpublishedPublications = publications.filter((pub) => pub.type === "unpublished");
---

<section id="publications" aria-labelledby="pub-title" class="py-10 border-t border-slate-200">
    <!-- Section Header -->
    <h2 id="pub-title" class="text-2xl text-sky-900 font-extrabold">{title}</h2>

    <!-- Filter Tabs -->
    <div class="my-6">
        <p class="text-sm text-slate-500 mb-3 sr-only">Filter:</p>

        <PublicationsFilter
            publishedPublicationsCount={publishedPublications.length}
            unpublishedPublicationsCount={unpublishedPublications.length}
            client:idle
        >
            <PublicationType slot="publishedPublications" title={publishedTitle}>
                <PublishedIcon slot="icon" size={24} class={typeStyles.published.textPrimary} />

                {
                    publishedPublications.map((publication, index) => (
                        <PublicationCard publication={publication} index={index} type="published" />
                    ))
                }
            </PublicationType>

            <PublicationType slot="unpublishedPublications" title={unpublishedTitle}>
                <UnpublishedIcon slot="icon" size={24} class={typeStyles.unpublished.textPrimary} />

                {
                    unpublishedPublications.map((publication, index) => (
                        <PublicationCard publication={publication} index={index} type="unpublished" />
                    ))
                }
            </PublicationType>
        </PublicationsFilter>
    </div>

    <p class="text-slate-500">{afterText}</p>
</section>

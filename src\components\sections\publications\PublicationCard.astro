---
import { AspectRatio } from "@/components/ui/aspect-ratio";
import { cn } from "@/lib/utils";
import { typeStyles, type Publication } from ".";

interface Props {
    publication: Publication;
    index: number;
    type: "published" | "unpublished";
}

const { publication, index, type } = Astro.props;

const currentStyles = typeStyles[type];
const formattedIndex = `[${index + 1}]`;
---

<article class="flex flex-col sm:flex-row gap-4">
    <!-- Left Side: Index Number and Thumbnail (outside card) -->
    <div class="relative flex-shrink-0 w-24">
        <AspectRatio ratio={210 / 297}>
            <img
                src="https://upload.wikimedia.org/wikipedia/commons/thumb/3/3f/C_size_illustration2.svg/500px-C_size_illustration2.svg.png"
                alt="Publication thumbnail"
                class="w-full h-full object-center"
            />
        </AspectRatio>
        <!-- Index number overlay on thumbnail -->
        <div
            class={cn(
                "absolute -top-1 -left-1 p-1 rounded-sm text-white grid place-items-center text-sm font-bold shadow-sm",
                currentStyles.bgSecondary,
                currentStyles.textPrimary
            )}
        >
            {formattedIndex}
        </div>
    </div>

    <!-- Right Side: Content Card -->
    <div class="bg-white border border-slate-200 rounded-lg shadow-sm p-3 sm:p-4 w-full flex flex-col">
        <h4 class="text-slate-800 font-semibold mb-1 leading-tight break-words" set:html={publication.title} />
        <p
            class="text-sm text-slate-500 mb-1 break-words -order-1 [&>strong]:underline [&>strong]:text-slate-600"
            set:html={publication.authors}
        />
        <p class="text-sm text-slate-600 mb-2 break-words" set:html={publication.journal} />

        <div class="flex flex-wrap items-center gap-2 sm:gap-4 text-xs">
            {
                publication.notes && Array.isArray(publication.notes) && publication.notes.length > 0 && (
                    <span class="px-2 py-1 bg-slate-100 rounded-sm font-semibold">
                        {publication.notes.map((note, index) => (
                            <span class={cn("break-words", index === 0 ? "text-red-600" : "text-green-600")}>
                                {note}
                            </span>
                        ))}
                    </span>
                )
            }
            {
                publication.furtherInfoLink && (
                    <a
                        href={publication.furtherInfoLink}
                        class={cn("underline break-words", currentStyles.textPrimary, currentStyles.textHover)}
                    >
                        {publication.furtherInfoText}
                    </a>
                )
            }
        </div>
    </div>
</article>

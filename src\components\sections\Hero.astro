---
import { Picture } from "astro:assets";
import type { Sections } from "@/data/portfolio";

interface Props {
    data: Sections["profile"];
}

const { name, jobTitle, contacts, profileImage, researchFieldsTitle, researchFields } = Astro.props.data;
---

<section id="profile" class="pt-10 sm:pt-14 pb-10">
    <div class="flex flex-col lg:flex-row gap-8 lg:gap-12 justify-center">
        <!-- Right Column: Profile Information -->
        <div class="order-2">
            <h1 class="text-3xl sm:text-4xl lg:text-5xl text-sky-900 font-extrabold leading-tight">
                {name}
            </h1>
            <p class="mt-2 text-lg text-slate-600 italic">{jobTitle}</p>

            <dl class="mt-6 grid grid-cols-1 gap-3" aria-label="Professional contact information">
                {
                    contacts.map((contact) => {
                        const IconComponent = contact.icon;
                        return (
                            <div class="bg-white border border-slate-200 rounded-lg px-4 py-3 shadow-sm">
                                <dt class="sr-only">{contact.type}</dt>
                                <dd class="gap-2 text-sm font-medium">
                                    <IconComponent size={20} class="inline w-5 h-5 text-sky-600 flex-shrink-0" />
                                    <span class="font-semibold text-sky-900">{contact.type}: </span>
                                    {contact.href ? (
                                        <a href={contact.href} class="hover:underline">
                                            {contact.value}
                                        </a>
                                    ) : (
                                        <span>{contact.value}</span>
                                    )}
                                </dd>
                            </div>
                        );
                    })
                }
            </dl>

            <div class="mt-6">
                <h2 class="text-xl text-sky-900 font-bold">{researchFieldsTitle}</h2>
                <ul class="mt-3 flex flex-wrap gap-2" role="list" aria-label="Research specialization tags">
                    {
                        researchFields.map((field) => (
                            <li>
                                <span class="inline-block bg-sky-50 text-sky-900 border border-sky-200 px-4 py-2 rounded-full text-sm font-medium hover:bg-sky-100 transition-colors">
                                    {field}
                                </span>
                            </li>
                        ))
                    }
                </ul>
            </div>
        </div>

        <!-- Left Column: Large Hero Profile Image -->
        <div class="order-1 lg:flex-shrink-0 lg:w-auto">
            <figure
                class="bg-transparent grid place-items-center shadow-xl max-w-80 sm:max-w-[400px] lg:mx-auto rounded-2xl overflow-hidden"
            >
                {
                    profileImage && (
                        <Picture
                            src={profileImage}
                            alt={`A portrait of ${name}`}
                            widths={[320, 400, 640, 800, profileImage.width]}
                            sizes="(max-width: 640px) 320px, 400px"
                            formats={["avif", "webp"]}
                            quality="high"
                            priority
                        />
                    )
                }
            </figure>
        </div>
    </div>
</section>

---
import { Image } from "astro:assets";
import type { Sections } from "@/data/portfolio";

interface Props {
    data: Sections["softwares"];
}

const { title, softwares } = Astro.props.data;
---

<section id="softwares" class="py-10 border-t border-slate-200">
    <h2 class="text-2xl text-sky-900 font-extrabold">{title}</h2>
    <p class="mt-2 text-sm text-slate-600">Placeholders for statistical software interfaces (Stata, SPSS).</p>
    {
        softwares && softwares.length > 0 && (
            <div class="mt-4 grid grid-cols-1 sm:grid-cols-2 gap-4">
                {softwares.map((software) => (
                    <figure class="bg-white border border-slate-200 rounded p-2">
                        <Image src={software.image} alt={software.alt} width={800} height={480} class="w-full h-auto" />
                        <figcaption class="text-xs text-slate-600 mt-2">{software.description}</figcaption>
                    </figure>
                ))}
            </div>
        )
    }
</section>
